{"$schema": "https://biomejs.dev/schemas/1.8.3/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noWith": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidNewBuiltin": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error", "useTemplate": "error"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "json": {"formatter": {"trailingCommas": "none"}}, "files": {"include": ["apps/**/*.{js,jsx,ts,tsx,json}", "packages/**/*.{js,jsx,ts,tsx,json}", "*.{js,jsx,ts,tsx,json}"], "ignore": ["node_modules/**", "dist/**", "build/**", ".next/**", "coverage/**", "*.min.js", "*.d.ts"]}}