{"name": "@sportsclub/ui", "version": "0.1.0", "description": "Shared UI components for Sportsclub platform", "main": "./src/index.tsx", "types": "./src/index.tsx", "exports": {".": "./src/index.tsx", "./button": "./src/button.tsx", "./card": "./src/card.tsx", "./input": "./src/input.tsx", "./modal": "./src/modal.tsx"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "check-types": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@sportsclub/shared-libs": "workspace:*", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"@sportsclub/eslint-config": "workspace:*", "@sportsclub/typescript-config": "workspace:*", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "typescript": "^5.8.2"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}