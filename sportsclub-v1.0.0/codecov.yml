# Codecov Configuration
# https://docs.codecov.com/docs/codecov-yaml

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 80%
        threshold: 1%
        if_ci_failed: error
    patch:
      default:
        target: 80%
        threshold: 1%
        if_ci_failed: error
    changes:
      default:
        if_ci_failed: error

  ignore:
    - "**/*.test.ts"
    - "**/*.test.tsx"
    - "**/*.spec.ts"
    - "**/*.spec.tsx"
    - "**/test/**"
    - "**/tests/**"
    - "**/__tests__/**"
    - "**/coverage/**"
    - "**/node_modules/**"
    - "**/.next/**"
    - "**/dist/**"
    - "**/build/**"
    - "**/*.config.js"
    - "**/*.config.ts"
    - "**/jest.config.js"
    - "**/next.config.js"
    - "**/tailwind.config.js"
    - "**/turbo.json"
    - "**/*.d.ts"

comment:
  layout: "reach,diff,flags,tree,reach"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes

github_checks:
  annotations: true

parsers:
  gcov:
    branch_detection:
      conditional: yes
      loop: yes
      method: no
      macro: no

flags:
  frontend:
    paths:
      - apps/web/
      - packages/ui/
      - packages/shared-libs/
  backend:
    paths:
      - packages/auth-service/
      - packages/prediction-service/
      - packages/data-ingestion-service/
      - packages/ai-assistant-service/
  shared:
    paths:
      - packages/shared-libs/

component_management:
  default_rules:
    flag_regexes:
      - name: frontend
        regex: ^frontend$
        statuses:
          - type: project
            target: 85%
      - name: backend
        regex: ^backend$
        statuses:
          - type: project
            target: 80%
      - name: shared
        regex: ^shared$
        statuses:
          - type: project
            target: 90%
