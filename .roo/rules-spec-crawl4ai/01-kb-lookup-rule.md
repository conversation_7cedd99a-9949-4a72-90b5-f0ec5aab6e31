+++
id = "KB-LOOKUP-SPEC-CRAWL4AI"
title = "KB Lookup Rule: Crawl4AI Specialist"
context_type = "rules"
scope = "Knowledge Base Lookup"
target_audience = ["spec-crawl4ai"]
granularity = "ruleset"
status = "active"
last_updated = "2025-04-18"
# version = ""
# related_context = []
tags = ["kb", "lookup", "rules", "spec-crawl4ai"]
# relevance = ""
target_mode_slug = "spec-crawl4ai"
kb_directory = ".ruru/modes/spec-crawl4ai/kb"
+++

# Knowledge Base Lookup Rule

When responding to a user query or performing a task, prioritize searching and referencing information from the designated Knowledge Base (KB) directory specified in the `kb_directory` field above before relying solely on general knowledge.

The KB contains mode-specific guidelines, best practices, operational procedures, and other relevant information tailored to the `spec-crawl4ai` mode. Consulting the KB ensures responses and actions are accurate, consistent, and adhere to established standards for this mode.