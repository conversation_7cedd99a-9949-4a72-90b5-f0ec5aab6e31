+++
# --- Metadata ---
id = "PLAYBOOK-DEMO-CODE-EXPLAINER-V1"
title = "Capability Playbook: AI-Powered Code Explanation Tool"
status = "draft" # Start as draft until tested
created_date = "2025-04-24"
updated_date = "2025-04-24"
version = "1.0"
tags = ["playbook", "documentation", "capability-demo", "ai-tool", "code-explanation", "llm", "openai", "huggingface", "diagram", "mermaid", "frontend", "epic", "feature", "task"]
related_docs = [
    ".ruru/docs/standards/project-management-strategy-v1.md",
    ".ruru/planning/project-structure/00-epic-feature-task-plan.md",
    ".ruru/modes/spec-openai/spec-openai.mode.md", # Example AI provider
    ".ruru/modes/spec-huggingface/spec-huggingface.mode.md", # Alternative AI provider
    ".ruru/modes/design-diagramer/design-diagramer.mode.md",
    ".ruru/modes/dev-general/dev-general.mode.md",
    ".ruru/modes/lead-frontend/lead-frontend.mode.md"
]
objective = "Guide the development of a simple web application that takes user-provided code, uses an AI model to generate an explanation, and optionally visualizes the code structure using Mermaid diagrams."
scope = "Covers frontend UI (input/output), integration with an AI explanation service (via specialist mode or MCP), optional diagram generation, and basic setup."
target_audience = ["Users", "Developers", "AI Modes"]
# --- Document Specific Fields ---
example_project_type = "Web-based Developer Utility"
ai_provider_mode_placeholder = "[AIProviderMode]" # e.g., "spec-openai", "mcp-vertex-ai-tool-explain-code"
diagram_library_placeholder = "Mermaid.js"
+++

# Capability Playbook: AI-Powered Code Explanation Tool

This playbook demonstrates how Roo Commander can orchestrate the creation of a functional web tool that leverages AI for code understanding and visualization.

**Scenario:** You want to build a simple web page where a user can paste a code snippet, click a button, and receive an explanation generated by an AI model (`[AIProviderMode]`), optionally accompanied by a Mermaid diagram visualizing the code's structure.

## Phase 1: Planning & Setup

1.  **Define the Tool's Goal (Epic/Feature):**
    *   **Goal:** Create a web-based tool to help developers understand code snippets using AI.
    *   **Action:** Create an Epic or Feature (e.g., `.ruru/features/FEAT-170-ai-code-explainer-tool.md`).
    *   **Content:** Define the `objective` (e.g., "Build a web UI for code explanation using `[AIProviderMode]` and optional `[diagram_library_placeholder]` visualization"), core functionality (paste code, get explanation, see diagram), target user (developers). Set `status` to "Planned".

2.  **Technology & Approach Decisions (ADR):**
    *   **Goal:** Decide on the specific AI provider/mode and diagramming approach.
    *   **Action:** Create an ADR (`.ruru/decisions/ADR-005-code-explainer-tech-stack.md`). Delegate analysis/recommendation to `core-architect` or `lead-frontend`.
    *   **Key Decisions:**
        *   AI Model/Mode: Which specific mode (`spec-openai`, `spec-huggingface`, or an MCP server tool) will generate the explanation? What are its input/output requirements?
        *   Diagramming: Will diagrams be generated? If so, use `design-diagramer` for Mermaid syntax? How will it be triggered (based on code, explanation, or specific user request)?
        *   Frontend Framework: Vanilla JS or a specific framework (React, Vue, Svelte)?

3.  **Basic Web Page Setup (Feature):**
    *   **Goal:** Create the minimal HTML/CSS/JS structure.
    *   **Action:** Define as a Feature (`FEAT-171-explainer-page-setup.md`). Delegate tasks to `dev-general` or framework specialist.
    *   **Tasks (Examples):**
        *   "Create `index.html` with code input (`textarea#code-input`), 'Explain' button (`button#explain-button`), explanation output area (`pre#explanation-output` or `div`), and diagram output area (`div#diagram-output`)."
        *   "Create basic `style.css`."
        *   "Create `script.js` and link it."
        *   *(If diagramming)* "Install/link `[diagram_library_placeholder]` library." (e.g., `npm install mermaid` or add CDN link).

## Phase 2: Core Functionality - AI Explanation

1.  **Implement UI Interaction (Feature):**
    *   **Goal:** Capture user input and trigger the explanation process.
    *   **Action:** Define as a Feature (`FEAT-172-explainer-ui-interaction.md`). Delegate tasks to `dev-general` or framework specialist.
    *   **Tasks (Examples):**
        *   "In `script.js`, add event listener to `button#explain-button`."
        *   "On click, read code from `textarea#code-input`."
        *   "Implement basic input validation (e.g., check if textarea is empty)."
        *   "Display a loading indicator."
        *   "Call the AI explanation function (to be created in next Feature)."

2.  **Integrate AI Explanation Service (Feature):**
    *   **Goal:** Send the code to the chosen AI and display the explanation.
    *   **Action:** Define as a Feature (`FEAT-173-integrate-[AIProviderMode]-explanation.md`). Delegate tasks based on the chosen AI approach (direct specialist call vs. backend wrapper).
    *   **Tasks (Example - Direct `spec-openai`):**
        *   *(Coordinator Task)* "Define the prompt structure for `spec-openai` (e.g., 'Explain the following [language, if detectable] code snippet:\n\n```\n{code}\n```\nFocus on its purpose, key logic, and potential improvements.')." Document this prompt.
        *   *(Frontend Task)* "Implement function `getExplanation(code)` in `script.js`."
        *   *(Frontend Task)* "Inside `getExplanation`, formulate the prompt using the code input."
        *   *(Frontend Task)* "Delegate the prompt execution to `spec-openai` via `new_task` (or appropriate mechanism if using MCP/backend)." **Requires secure API key handling for `spec-openai`.** Best practice is usually a backend proxy, but for a simple demo, direct delegation *might* be acceptable if the mode handles its own credentials securely via environment variables accessible *only* to it.
        *   *(Frontend Task)* "Handle the asynchronous response from the AI."
        *   *(Frontend Task)* "On success, display the explanation text in `pre#explanation-output`."
        *   *(Frontend Task)* "Implement error handling (display error message if AI call fails)."
        *   *(Frontend Task)* "Hide loading indicator."
    *   **Process:** Use MDTM workflow. Requires careful consideration of API key security.

## Phase 3: Diagram Generation & Display (Optional Feature)

1.  **Implement Diagram Generation Logic (Feature):**
    *   **Goal:** Generate Mermaid syntax based on the code or explanation.
    *   **Action:** Define as a Feature (`FEAT-174-explainer-diagram-generation.md`).
    *   **Tasks (Examples):**
        *   *(Coordinator/Architect Task)* "Decide trigger: Generate diagram based on original code or the AI's explanation?" Document choice.
        *   *(Frontend Task)* "Modify `script.js`: After receiving the AI explanation (or based on original code), formulate a request for `design-diagramer` (e.g., 'Generate a Mermaid sequence diagram for this code: {code}' or 'Generate a flowchart for this explanation: {explanation}')."
        *   *(Frontend Task)* "Delegate the diagram request to `design-diagramer` via `new_task`."
        *   *(Frontend Task)* "Handle the asynchronous response (containing Mermaid syntax)."
        *   *(Frontend Task)* "Store the returned Mermaid syntax."

2.  **Implement Diagram Rendering (Tasks):**
    *   **Goal:** Display the generated Mermaid diagram on the page.
    *   **Action:** Add Tasks to Feature FEAT-174. Delegate to `dev-general` or framework specialist.
    *   **Tasks (Examples):**
        *   "Write JavaScript function `renderDiagram(mermaidSyntax)`."
        *   "Inside function, select `div#diagram-output`."
        *   "Use the `[diagram_library_placeholder]` API (e.g., `mermaid.render()`) to render the syntax into the diagram output div."
        *   "Handle errors during rendering."
        *   "Call `renderDiagram` after successfully receiving Mermaid syntax."

## Phase 4: Styling, Testing & Refinement

1.  **Apply Styling:**
    *   **Goal:** Make the tool visually presentable.
    *   **Action:** Define Tasks. Delegate to `design-tailwind` or `design-ui`.
    *   **Process:** Style the textarea, button, output areas, loading indicator, error messages. Ensure responsive layout.

2.  **Manual Testing:**
    *   **Goal:** Verify the end-to-end flow with various code snippets.
    *   **Action:** Test different code inputs (different languages, simple, complex, erroneous code). Verify explanation quality, diagram generation (if applicable), loading states, and error handling. Create bug-fix tasks as needed.

3.  **Refine Prompts/Logic:** Based on testing, refine the AI explanation prompt or diagram generation request for better results.

## Phase 5: Documentation & Deployment

1.  **Write README:**
    *   **Goal:** Document the tool's purpose, how to set it up (including API keys), and how to use it.
    *   **Action:** Define Task. Delegate to `util-writer`.

2.  **Deployment (Optional):**
    *   **Goal:** Make the tool accessible online.
    *   **Action:** Define Feature/Tasks. Delegate to `lead-devops`.
    *   **Process:** Deploy as a static site (e.g., GitHub Pages, Vercel, Netlify) if using client-side AI calls. If a backend wrapper was built, deploy both frontend and backend.

## Key Considerations:

*   **API Key Security:** If calling AI services directly from the frontend (`spec-openai`), API keys are exposed. **This is generally insecure for public applications.** The standard secure approach is to build a simple backend API wrapper that holds the key and proxies requests. For an internal demo or personal tool where the user configures their *own* key via the mode's settings or an MCP server, direct delegation *might* be acceptable.
*   **Prompt Quality:** The usefulness of the explanation heavily depends on the prompt sent to the AI model.
*   **Diagram Feasibility:** Generating accurate diagrams automatically from arbitrary code or text is challenging. `design-diagramer`'s success will depend on the complexity of the input and the clarity of the request. Manage user expectations.
*   **Error Handling:** Handle API errors from the AI provider, diagram generation errors, and invalid user input gracefully.
*   **Cost:** Be mindful of AI API costs if using paid services.

This playbook provides a framework for building an interesting AI-powered developer utility, highlighting integration points and potential challenges like security.