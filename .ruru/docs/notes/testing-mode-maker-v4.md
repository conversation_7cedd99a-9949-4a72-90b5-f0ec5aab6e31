lets try v4 of mode maker, to make a golang mode and see if we can get really detailed kb content!

ok we have got the basic parameters figured out

time for the vertec ai mcp to be used for research!

sometimes these queries take a little while, depends on how long vertex needs 

looks promising so far

the alternative i have in mind for this is to create synth content from context7

golong is a fairly big topic area

if we were to try and use the context7 file its 670,714 tokens!

hopefully this all correct, i dont really know anything about golang

ive tried to give the ai liberty to decide how much research to do rather than making hard rules for it

this is where ive asked it to attempt to figure out how complex/deep the topics are

not sure if this will work, looks ok?

cool, its checking the template etc

not sure if this is exactly going to plan, im hoping it will make a lot of good content, i might need to revise the part where we send it to context condenser

mmm, idk, seems like its missing a lot, its a whole programming language....

ok this is interesting, its making the sub folders of the kb

im not sure if this is comprehensive enough but it looks reasonable? a good starting point anyway.

ok i think its time other people get to test this!