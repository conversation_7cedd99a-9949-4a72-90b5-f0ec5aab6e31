# 07 Best Practices & Troubleshooting

This section provides tips for using Roo Commander effectively, including best practices for prompting, managing context, and troubleshooting common issues.

## Files in this section

*   [01 Effective Prompting Delegation](01_Effective_Prompting_Delegation.md)
*   [02 Managing AI Context](02_Managing_AI_Context.md)
*   [03 Troubleshooting Common Issues](03_Troubleshooting_Common_Issues.md)
*   [04 Reviewing AI Output](04_Reviewing_AI_Output.md)

[Back to Main KB README](../README.md)