{"name": "@sportsclub/eslint-config", "version": "0.1.0", "description": "ESLint configuration for Sportsclub platform", "main": "index.js", "files": ["index.js", "next.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0"}, "devDependencies": {"eslint": "^9.29.0", "typescript": "^5.8.2"}, "peerDependencies": {"eslint": "^9.0.0", "typescript": "^5.0.0"}}