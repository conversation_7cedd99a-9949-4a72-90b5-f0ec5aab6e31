+++
# --- Basic Metadata ---
id = "KB-RC-MODES-REFERENCE"
title = "Understanding Modes: Mode Directory Reference"
status = "draft"
doc_version = "1.0" # Version of this reference document
content_version = 1.0
audience = ["users", "developers", "architects", "contributors", "ai_modes"]
last_reviewed = "2025-04-28" # Use current date
template_schema_doc = ".ruru/templates/toml-md/09_documentation.README.md"
tags = ["roo-commander", "modes", "reference", "list", "directory", "capabilities", "core-concept"]
related_docs = [
    "../README.md", # Link to the KB README
    "01_Mode_Roles_Hierarchy.md",
    "02_Mode_Selection_Guide.md",
    "../../../../.ruru/modes/roo-commander/kb/kb-available-modes-summary.md" # Link to the generated summary
    ]
+++

# Understanding Modes: Mode Directory Reference

## 1. Introduction / Purpose 🎯

This document serves as a central reference point for discovering the available AI agent modes within the Roo Commander framework. It explains where to find the definitive list of modes and how to access more detailed information about each mode's specific capabilities and functions.

## 2. Authoritative Source: The Generated Summary 📌

The **single source of truth** for the list of currently available modes, their display names, and their core purpose (summary) is the automatically generated file located at:

*   **`.ruru/modes/roo-commander/kb/kb-available-modes-summary.md`**

This file is generated by the `build_roomodes.js` script (or a similar build process) by scanning the `.ruru/modes/` directory and parsing the `.mode.md` definition file for each valid mode found.

**Why a Generated File?**
Using a generated summary ensures the list is always up-to-date with the actual modes configured in the workspace, preventing documentation drift.

## 3. Content of the Summary File 📄

The `kb-available-modes-summary.md` file provides the following for each mode:

*   **Slug:** The unique, machine-readable identifier (e.g., `dev-react`, `util-writer`).
*   **Name:** The human-readable display name, including its emoji (e.g., `⚛️ React Specialist`, `✍️ Technical Writer`).
*   **Summary:** A concise, one-sentence description of the mode's core purpose (extracted from the `summary` field in the mode's definition file).

The modes in the summary file are typically grouped by their classification/domain (e.g., Core, Leads, Workers, Agents, Frameworks, Design, etc.) for easier browsing.

## 4. Finding Detailed Capabilities & Usage 💡

While the generated summary provides a quick overview, you often need more detail about a specific mode's capabilities, typical workflow, limitations, or configuration. To find this information:

1.  **Mode Definition File (`.mode.md`):** The most detailed information resides within the mode's own definition file:
    *   **Location:** `.ruru/modes/[mode_slug]/[mode_slug].mode.md`
    *   **Content:** Contains the full system prompt, detailed capability lists, workflow descriptions, usage examples, limitations, rationale, tool access, file restrictions, and metadata.
2.  **Mode Selection Guide (`02_Mode_Selection_Guide.md`):** This guide (located in the same directory as this file) includes a section that lists modes along with their core purpose and *inferred/generated* key capabilities, providing a slightly more detailed overview than the basic summary file.
3.  **Mode-Specific KB (`.ruru/modes/[mode_slug]/kb/`):** For modes with complex procedures or extensive reference material, consult their dedicated Knowledge Base directory.

## 5. Accessing the Summary

You can access the list of modes in a few ways:

*   **Directly:** Open the file `.ruru/modes/roo-commander/kb/kb-available-modes-summary.md` in your editor.
*   **Ask Commander:** Ask Roo Commander (or `session-manager`) to display the list, e.g., "Show me the available modes" or "List all specialist modes". The AI should read and present the content of the summary file.

## 6. Conclusion ✅

This reference points you to the dynamically generated `kb-available-modes-summary.md` as the authoritative list of modes. For understanding the *purpose* of each mode, the summary is sufficient. For understanding *detailed capabilities, workflow, and usage*, consult the individual `.mode.md` files or the enhanced Mode Selection Guide.