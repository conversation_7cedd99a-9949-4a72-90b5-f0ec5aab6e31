{"name": "sportsclub-platform", "version": "1.0.0", "description": "Sportsclub - Sports Prediction & Betting Platform", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "test": "turbo run test", "check-types": "turbo run check-types", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "bmad:handoff": "node scripts/bmad-roo-handoff.js", "bmad:validate": "node scripts/bmad-roo-handoff.js --validate", "roo:start": "echo '🚀 Use @roo-commander to start implementation'", "roo:queue": "ls -la .roo/story-queue/ 2>/dev/null || echo '📭 No stories queued yet'", "roo:next": "echo '🎯 Use @roo-commander to get next story'", "roo:modes": "cat .roomodes"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.29.0", "lefthook": "^1.12.1", "prettier": "^3.0.0", "turbo": "^2.5.0", "typescript": "^5.8.2"}, "dependencies": {"js-yaml": "^4.1.0"}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}}