{"ci": {"collect": {"url": ["http://localhost:3000", "http://localhost:3000/dashboard", "http://localhost:3000/predictions", "http://localhost:3000/leaderboard"], "startServerCommand": "pnpm dev", "startServerReadyPattern": "ready", "startServerReadyTimeout": 30000, "numberOfRuns": 3}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.8}], "categories:seo": ["warn", {"minScore": 0.8}], "categories:pwa": ["warn", {"minScore": 0.6}]}}, "upload": {"target": "temporary-public-storage"}}}