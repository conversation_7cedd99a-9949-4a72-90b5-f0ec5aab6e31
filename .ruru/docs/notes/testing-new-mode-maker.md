Lots of improvements to the mode maker... lets try it again!


a few questions to get things rolling 

check to see if it exists already

and time for researching context

this time we are using the marvellous vertex ai mcp by shariq

the explain docs mode will try to use official documentation

sometimes takes a little while for it to run all the query with vertex

now its doing some investigating to see what else it might need to know to work on python

the intention is to lean into the official source docs

without me having to give it the right url

vertex ai search and summary is quite powerful

this is all using the free gemini 2.5 exp

there's no set limit on how much research it will do, the ai has liberty to decide what and how much it researches

i dont really want to have to approve all this, i dont mind if it runs for a while.

if you picked a smaller topic or tech it would take less time

cool nice summary time

context condenser is checking the synthetic task sets, the

it makes the rules folder

and the mode folders

where we store all the context, kb etc

that was the mode data file

we use it to make summaries, and the roomodes file etc

reading and writing with the vertex mcp to speed things up

reduces context and time a bit

just gone 15min

cleans up temp files

i refreshed a bit too soon, its a big chat log, it will open but might take a while. :)

next improvement, making more in depth context files!