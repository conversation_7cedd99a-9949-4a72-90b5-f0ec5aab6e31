{"name": "@sportsclub/web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "lint:fix": "next lint --fix", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next dist coverage"}, "packageManager": "pnpm@9.0.0", "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@sportsclub/ui": "workspace:*", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@sportsclub/eslint-config": "workspace:*", "@sportsclub/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.29.0", "typescript": "5.8.2"}}