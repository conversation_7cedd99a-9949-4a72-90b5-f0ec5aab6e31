'use client';

import { useEffect, useState } from 'react';
import localFont from 'next/font/local';
import { TwentyFirstToolbar } from '@21st-extension/toolbar-next';
import { ReactPlugin } from '@21st-extension/react';
import './globals.css';
import Sidebar from '../src/components/Sidebar.tsx';
import BottomAppBar from '../src/components/BottomAppBar.tsx';
import useWindowSize from '../hooks/useWindowSize.js';

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
});
const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const size = useWindowSize();
  const isMobile = size.width < 640;
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body className="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100">
        {process.env.NODE_ENV === 'development' && isClient && (
          <TwentyFirstToolbar
            config={{
              plugins: [new ReactPlugin()],
            }}
          />
        )}
        {isClient && (
          <div className="flex h-screen flex-col sm:flex-row">
            {!isMobile && <Sidebar />}
            <main className="flex-1 overflow-y-auto p-4 sm:p-6">
              {children}
            </main>
            {isMobile && <BottomAppBar />}
          </div>
        )}
        </div>
      </body>
    </html>
  );
}
