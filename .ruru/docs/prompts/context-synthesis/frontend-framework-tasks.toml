You are an expert technical writer and AI prompt engineer designing configuration for an automated knowledge base (KB) synthesis system.

Your goal is to generate the content for a TOML file named `frontend-framework-tasks.toml`. This file defines specific AI synthesis tasks tailored for frontend JavaScript frameworks like React, Vue, Angular, SvelteKit, Next.js, Remix, Astro, etc.

The output TOML **MUST** strictly adhere to the following structure, defined by the system's `README.md` for task set templates:

--- START OF SCHEMA DEFINITION ---

# TOML definition for synthesis tasks for a specific library type.

# Required: Identifies the type this task set applies to. Matches the key used in library-types.json.
library_type = "example-type" # Replace with the actual type

# Required: An array of task tables. Each table defines one synthesis task.
[[tasks]]
  # Required: Unique identifier for this task within the set. (e.g., "core_concepts", "component_props_summary")
  task_id = "task_identifier_1"

  # Required: Human-readable description of the task's goal.
  description = "Generate an overview of core concepts and principles."

  # Required: List of source KB category directory names to use as input for this task.
  # The synthesizer will read all .md files from these categories within the library's source KB.
  # Common categories include: "guide", "api", "reference", "concepts", "tutorial", "cookbook", "examples", "config", "start", "installation", "misc", "about"
  input_categories = ["guide", "concepts", "about"]

  # Required: The base filename for the synthesized output markdown file.
  # It will be saved in `.ruru/modes/{mode_slug}/kb/{library_name}/synthesized/`.
  output_filename = "core-concepts-summary.md"

  # Required: Specific instructions/prompt focus for the agent-context-synthesizer mode.
  # This tells the AI *what* to focus on when reading the input files for this specific task.
  prompt_focus = "Identify and explain the fundamental ideas, design philosophy, and main features based *only* on the provided input files. Aim for a conceptual overview."

# Add more [[tasks]] tables as needed for this library type.

--- END OF SCHEMA DEFINITION ---

Now, generate the TOML content for `frontend-framework-tasks.toml`. Ensure you set `library_type = "frontend-framework"`.

Include distinct `[[tasks]]` for the following key aspects commonly found in frontend frameworks (feel free to add others if highly relevant):

1.  **Core Concepts & Philosophy:** Overview of the framework's main ideas, reactivity model, architecture (e.g., components, virtual DOM, signals, islands).
2.  **Setup & Installation:** Common ways to initialize a project, basic configuration.
3.  **Component Model:** How components are defined, props, state, events/emitters, slots/children.
4.  **Templating & Rendering:** Syntax used in templates (JSX, template syntax), directives, conditional rendering, list rendering.
5.  **Routing:** How client-side or server-side routing is typically handled.
6.  **State Management:** Common built-in or recommended external state management solutions and patterns.
7.  **Lifecycle Hooks:** Key points in a component's lifecycle and associated functions/hooks.
8.  **API Interaction:** Common patterns for fetching data or interacting with backend APIs.
9.  **Styling:** Typical approaches to styling components.

For each task, define a unique `task_id`, a clear `description`, suggest appropriate `input_categories`, choose a descriptive `output_filename` (ending in `.md`), and write a concise but specific `prompt_focus` instructing the synthesizer AI.

Output **ONLY** the raw TOML content suitable for saving directly into the `frontend-framework-tasks.toml` file. Do not include any explanatory text before or after the TOML content itself.