# Database Configuration
DATABASE_URL=postgresql://sportsclub:sportsclub_dev@localhost:5432/sportsclub
REDIS_URL=redis://localhost:6379
MONGODB_URL=**************************************************************

# Service URLs
AUTH_SERVICE_URL=http://localhost:8001
PREDICTION_SERVICE_URL=http://localhost:8002
DATA_INGESTION_SERVICE_URL=http://localhost:8003
AI_ASSISTANT_SERVICE_URL=http://localhost:8004
LEADERBOARD_SERVICE_URL=http://localhost:8005
NOTIFICATION_SERVICE_URL=http://localhost:8006

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# External APIs
SPORTS_API_KEY=your-sports-api-key
ODDS_API_KEY=your-odds-api-key

# AI/ML Configuration
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=sportsclub-uploads

# Application Configuration
NODE_ENV=development
LOG_LEVEL=debug
CORS_ORIGIN=http://localhost:3000
