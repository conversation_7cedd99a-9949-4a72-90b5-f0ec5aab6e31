{"name": "@sportsclub/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "clean": "rm -rf .next dist"}, "dependencies": {"@sportsclub/shared-libs": "workspace:*", "@sportsclub/ui": "workspace:*", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.0", "framer-motion": "^11.0.0", "axios": "^1.6.0", "zustand": "^4.4.0", "@tanstack/react-query": "^5.0.0"}, "devDependencies": {"@sportsclub/eslint-config": "workspace:*", "@sportsclub/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "autoprefixer": "^10.4.0", "eslint": "^9.29.0", "postcss": "^8.4.0", "typescript": "^5.8.2"}}