name: 🚀 CI Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}

jobs:
  changes:
    name: 🔍 Detect Changes
    runs-on: ubuntu-latest
    outputs:
      packages: ${{ steps.changes.outputs.packages }}
      apps: ${{ steps.changes.outputs.apps }}
      infra: ${{ steps.changes.outputs.infra }}
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔍 Detect Changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            packages:
              - 'packages/**'
            apps:
              - 'apps/**'
            infra:
              - 'infra/**'
              - '.github/workflows/**'

  setup:
    name: 🛠️ Setup Environment
    runs-on: ubuntu-latest
    outputs:
      node-version: ${{ steps.versions.outputs.node-version }}
      python-version: ${{ steps.versions.outputs.python-version }}
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 📋 Read Versions
        id: versions
        run: |
          echo "node-version=18" >> $GITHUB_OUTPUT
          echo "python-version=3.11" >> $GITHUB_OUTPUT

  lint-and-format:
    name: 🧹 Lint & Format
    runs-on: ubuntu-latest
    needs: [setup, changes]
    if: needs.changes.outputs.packages == 'true' || needs.changes.outputs.apps == 'true'
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.setup.outputs.node-version }}
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📂 Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 🗄️ Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🧹 Run ESLint
        run: pnpm turbo run lint

      - name: 💅 Check Prettier formatting
        run: pnpm run format:check

  type-check:
    name: 🔍 Type Check
    runs-on: ubuntu-latest
    needs: [setup, changes]
    if: needs.changes.outputs.packages == 'true' || needs.changes.outputs.apps == 'true'
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.setup.outputs.node-version }}
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📂 Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 🗄️ Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔍 Type check
        run: pnpm turbo run check-types

  test:
    name: 🧪 Test
    runs-on: ubuntu-latest
    needs: [setup, changes]
    if: needs.changes.outputs.packages == 'true' || needs.changes.outputs.apps == 'true'
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: sportsclub_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.setup.outputs.node-version }}
          cache: 'npm'

      - name: 🐍 Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ needs.setup.outputs.python-version }}

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📦 Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: ${{ needs.setup.outputs.python-version }}

      - name: 📂 Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 🗄️ Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 📦 Install Node.js dependencies
        run: pnpm install --frozen-lockfile

      - name: 📦 Install Python dependencies
        run: |
          for service in packages/auth-service packages/prediction-service packages/data-ingestion-service packages/ai-assistant-service; do
            if [ -f "$service/pyproject.toml" ]; then
              echo "Installing dependencies for $service"
              cd "$service"
              pdm install
              cd ../..
            fi
          done

      - name: 🧪 Run tests
        run: pnpm turbo run test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/sportsclub_test
          REDIS_URL: redis://localhost:6379

      - name: 📊 Upload coverage reports
        uses: codecov/codecov-action@v4
        if: always()
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info
          fail_ci_if_error: false

  build:
    name: 🏗️ Build
    runs-on: ubuntu-latest
    needs: [setup, changes, lint-and-format, type-check]
    if: needs.changes.outputs.packages == 'true' || needs.changes.outputs.apps == 'true'
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ needs.setup.outputs.node-version }}
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📂 Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: 🗄️ Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🏗️ Build packages
        run: pnpm turbo run build

      - name: 📦 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            apps/web/.next/
            packages/*/dist/
          retention-days: 1

  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: [changes]
    if: needs.changes.outputs.packages == 'true' || needs.changes.outputs.apps == 'true'
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🔒 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 🔍 Audit npm dependencies
        run: |
          cd sportsclub-v1.0.0
          pnpm audit --audit-level moderate

  summary:
    name: 📋 CI Summary
    runs-on: ubuntu-latest
    needs: [lint-and-format, type-check, test, build, security]
    if: always()
    steps:
      - name: 📋 Generate Summary
        run: |
          echo "## 🚀 CI Pipeline Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Job | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Lint & Format | ${{ needs.lint-and-format.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Type Check | ${{ needs.type-check.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Tests | ${{ needs.test.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Build | ${{ needs.build.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.security.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
