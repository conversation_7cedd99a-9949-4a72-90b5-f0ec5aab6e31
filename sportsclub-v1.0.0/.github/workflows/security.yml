name: 🔒 Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

concurrency:
  group: security-${{ github.ref }}
  cancel-in-progress: true

jobs:
  dependency-scan:
    name: 🔍 Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔍 Audit npm dependencies
        run: |
          cd sportsclub-v1.0.0
          pnpm audit --audit-level moderate --json > npm-audit.json || true

      - name: 📊 Upload npm audit results
        uses: actions/upload-artifact@v4
        with:
          name: npm-audit-results
          path: npm-audit.json

  code-scan:
    name: 🔍 Code Security Scan
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🔍 Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, python
          queries: security-extended,security-and-quality

      - name: 🏗️ Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:javascript,python"

  container-scan:
    name: 🐳 Container Security Scan
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    strategy:
      matrix:
        service: [auth-service, prediction-service, data-ingestion-service, ai-assistant-service]
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🏗️ Build container image
        uses: docker/build-push-action@v5
        with:
          context: packages/${{ matrix.service }}
          file: packages/${{ matrix.service }}/Dockerfile
          load: true
          tags: sportsclub-${{ matrix.service }}:scan
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🔒 Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: sportsclub-${{ matrix.service }}:scan
          format: 'sarif'
          output: 'trivy-${{ matrix.service }}.sarif'

      - name: 📊 Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.service }}.sarif'
          category: 'container-${{ matrix.service }}'

  secrets-scan:
    name: 🔐 Secrets Detection
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔐 Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  license-scan:
    name: 📄 License Compliance
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 📄 Check licenses
        run: |
          npx license-checker --summary --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;0BSD' || true

  security-report:
    name: 📊 Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-scan, container-scan, secrets-scan, license-scan]
    if: always()
    steps:
      - name: 📊 Generate Security Summary
        run: |
          echo "## 🔒 Security Scan Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Scan Type | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Scan | ${{ needs.dependency-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Code Security | ${{ needs.code-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Container Scan | ${{ needs.container-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Secrets Detection | ${{ needs.secrets-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| License Compliance | ${{ needs.license-scan.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Scan Date**: $(date -u +'%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY

      - name: 📢 Notify on security issues
        if: needs.dependency-scan.result == 'failure' || needs.code-scan.result == 'failure' || needs.container-scan.result == 'failure' || needs.secrets-scan.result == 'failure'
        run: |
          echo "🚨 Security issues detected!"
          # Add notification logic for security issues
