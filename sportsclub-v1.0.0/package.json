{"name": "sportsclub-platform", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "test": "turbo run test", "test:watch": "turbo run test:watch", "test:coverage": "turbo run test:coverage", "check-types": "turbo run check-types", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "commit": "cz", "prepare": "lefthook install", "ci": "pnpm run lint && pnpm run check-types && pnpm run test && pnpm run build", "precommit": "pnpm run format:check && pnpm run lint && pnpm run check-types"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "commitizen": "^4.3.1", "commitlint": "^19.8.1", "cz-conventional-changelog": "^3.3.0", "cz-git": "^1.12.0", "eslint": "^9.29.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "lefthook": "^1.12.1", "lint-staged": "^13.2.2", "prettier": "^2.8.8", "turbo": "^2.5.0"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "git add"], "*.{json,css,md}": ["prettier --write", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}