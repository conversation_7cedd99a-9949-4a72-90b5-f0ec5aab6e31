{"$schema": "https://turborepo.com/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"]}, "test": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "src/**/*.test.*", "tests/**/*", "test/**/*"]}, "check-types": {"dependsOn": ["^check-types"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json", "src/**/*.ts", "src/**/*.tsx"]}, "clean": {"cache": false}}, "globalDependencies": ["package.json", "pnpm-workspace.yaml", "turbo.json", ".env*"]}