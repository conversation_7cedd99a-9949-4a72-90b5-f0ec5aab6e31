{"name": "bmad-roo-project", "version": "1.0.0", "description": "BMAD + Roo Commander integrated project", "packageManager": "pnpm@9.0.0", "scripts": {"bmad:handoff": "node scripts/bmad-roo-handoff.js", "bmad:validate": "node scripts/bmad-roo-handoff.js --validate", "roo:start": "echo '🚀 Use @roo-commander to start implementation'", "roo:queue": "ls -la .roo/story-queue/ 2>/dev/null || echo '📭 No stories queued yet'", "roo:next": "echo '🎯 Use @roo-commander to get next story'", "roo:modes": "cat .roomodes"}, "dependencies": {"js-yaml": "^4.1.0"}}