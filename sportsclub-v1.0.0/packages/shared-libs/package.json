{"name": "@sportsclub/shared-libs", "version": "0.1.0", "description": "Shared types, constants, and utilities for Sportsclub platform", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./types": "./src/types/index.ts", "./constants": "./src/constants/index.ts", "./utils": "./src/utils/index.ts", "./config": "./src/config/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf dist coverage"}, "dependencies": {"zod": "^3.22.0", "date-fns": "^3.0.0"}, "devDependencies": {"@sportsclub/eslint-config": "workspace:*", "@sportsclub/typescript-config": "workspace:*", "@types/jest": "^29.5.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "typescript": "^5.8.2"}}