## GOAL: Generate YouTube Video Metadata for a Roo Commander on Roo Code the AI Web Development Platform in VS Code

Based on the specific video details provided below, generate the following YouTube metadata:
1.  **Multiple distinct title suggestions** (4-5 options)
2.  **A detailed video description** (formatted as plain text, using emojis/Unicode for structure)
3.  **A detailed prompt** for an AI image generator (like Midjourney, DALL-E, etc.) to create a compelling thumbnail.

---

## Video Specific Details (Fill this in for each video)

*   **[Brief Video Summary]:** (1-2 sentences describing the main focus/goal of *this specific* video demo. E.g., "This video demos Roo Commander building a React component library from a design spec.")
*   **[Key Tasks/Challenges Demonstrated]:** (List the main things the AI was asked to do. Use bullet points. E.g.,
    *   Generating React components based on Figma descriptions.
    *   Writing unit tests for the components.
    *   Refactoring code based on feedback.
    *   Integrating components into a Storybook.)
*   **[Specific Roo Code Features/Modes Highlighted]:** (List any specific modes or capabilities shown prominently. E.g.,
    *   Roo Commander task delegation
    *   React Component Generation Mode
    *   Code Refactoring Mode
    *   Test Generation)
*   **[Assets/Tools Used (if notable)]:** (List any specific frameworks, libraries, or unique assets used. E.g., React, Storybook, Figma link (optional), specific NPM packages.)
*   **[Key Timestamps]:** (List important moments with brief descriptions. Use HH:MM:SS or MM:SS format. E.g.,
    *   00:00 - Intro & Goal
    *   01:15 - Initial Prompt to Roo Commander
    *   05:30 - Reviewing Generated Components
    *   12:45 - Adding Unit Tests
    *   18:20 - Refactoring Example
    *   25:00 - Final Result & Conclusion)
*   **[Main Visual Element/Concept for Thumbnail]:** (Describe the most visually representative or interesting part of *this* demo to potentially feature in the thumbnail. E.g., "Showing code diff view for refactoring," or "Split screen: Figma design vs. generated component," or "Abstract visualization of component generation.")

---

## AI Generation Task

Please generate the following based on the details above:

### 1. YouTube Title Suggestions (4-5 Options)
*   Generate 4-5 distinct, engaging titles.
*   Ensure titles include "Roo Code" and/or "Roo Commander".
*   Mention key technologies/tasks shown (e.g., "React", "VS Code", "Component Library").
*   Vary the style (e.g., descriptive, question-based, benefit-focused).

### 2. YouTube Description
*   **Format:** Plain text suitable for copy-pasting into YouTube. Use emojis (like ✨, 🚀, 💡, 🛠️, ⏰, 👇, 👍, 🔔) and simple Unicode separators (like --- or `🔹➡️`) for structure and visual appeal where appropriate. Make sure timestamps are in a clickable format (MM:SS or HH:MM:SS). URLs and #hashtags should also be formatted correctly.
*   **Content Structure:**
    *   **Hook:** Start with an engaging sentence or two summarizing the video (use `[Brief Video Summary]`).
    *   **Demo Overview:** Briefly explain the main tasks/challenges shown (use `[Key Tasks/Challenges Demonstrated]`). Include the clickable timestamps provided in `[Key Timestamps]`.
    *   **Key Features/Modes:** Detail the specific Roo Code capabilities showcased (use `[Specific Roo Code Features/Modes Highlighted]`). Use bullet points or emoji markers (🔹).
    *   **Tools/Tech:** List the relevant tools and technologies used (use `[Assets/Tools Used (if notable)]`).
    *   **🔗 Useful Links:**
        *   Roo Code: https://roocode.com/
        *   Roo Code Docs: https://docs.roocode.com/
        *   Roo Commander (GitHub): https://github.com/jezweb/roo-commander
        *   Roo Commander Community (Discord): https://discord.gg/ESaJBnw7As
        *   Buy Me a Coffee: https://buymeacoffee.com/jezweb
    *   **Timestamps Section:** Clearly list the key moments using the provided `[Key Timestamps]`.
    *   **Call to Action:** Include a prompt for comments, likes, and subscriptions.
    *   **Hashtags:** Generate relevant hashtags (e.g., #RooCode, #RooCommander, #AICodingAssistant, #VSCode, #CodeGeneration, #[SpecificTechnology], #AIDemo, #Programming).

### 3. Thumbnail Generation Prompt (Detailed)
*   Generate a *detailed text prompt* suitable for an AI image generator (like Midjourney, DALL-E, Stable Diffusion).
*   The prompt should aim to create a visually interesting, tech-focused thumbnail.
*   **Instructions for the Image AI should include:**
    *   **Style/Mood:** (e.g., futuristic, clean tech, dynamic, slightly abstract).
    *   **Background:** (Likely a dark, stylized VS Code interface or abstract code background).
    *   **Key Elements:** Incorporate the `[Main Visual Element/Concept for Thumbnail]` from the video details. Consider abstractly representing "Roo Commander" or "AI Modes" (e.g., glowing console, data streams, connected nodes). *Do not* ask it to generate the specific Roo Code logo.
    *   **Text Overlay:** Include clear, bold text like "Roo Code", "Roo Commander", or a short phrase capturing the video's essence (e.g., "AI Builds React Lib"). Specify a modern font style.
    *   **Color Palette:** Suggest contrasting, vibrant tech colors (blues, purples, greens) against the dark background.
    *   **Composition:** Suggest a dynamic arrangement of elements.

### Tone and Style
*   The generated titles and description should be informative, engaging, and targeted towards developers or those interested in AI coding tools.
*   The thumbnail prompt should be descriptive and provide enough detail for an image AI to generate a relevant and eye-catching image.

### Final Output Format
*   Provide the titles as a numbered list.
*   Provide the description as a single block of formatted plain text, ready to copy and paste.
*   Provide the thumbnail prompt as a single block of text.