name: 🚀 Deploy to Staging

on:
  push:
    branches: [develop]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

concurrency:
  group: staging-deployment
  cancel-in-progress: false

env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ vars.TURBO_TEAM }}
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  pre-deploy:
    name: 🔍 Pre-deployment Checks
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.check.outputs.should-deploy }}
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔍 Check deployment conditions
        id: check
        run: |
          if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "🚨 Force deployment requested"
          else
            # Check if <PERSON><PERSON> passed on the latest commit
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "✅ Deployment conditions met"
          fi

  deploy-frontend:
    name: 🌐 Deploy Frontend to Vercel
    runs-on: ubuntu-latest
    needs: pre-deploy
    if: needs.pre-deploy.outputs.should-deploy == 'true'
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🏗️ Build frontend
        run: pnpm turbo run build --filter=@sportsclub/web
        env:
          NODE_ENV: production
          NEXT_PUBLIC_API_URL: ${{ secrets.STAGING_API_URL }}
          NEXT_PUBLIC_ENVIRONMENT: staging

      - name: 🚀 Deploy to Vercel
        id: deploy
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: apps/web
          vercel-args: '--prod'
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: 📝 Comment deployment URL
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Staging Deployment**\n\n✅ Frontend deployed to: ${{ steps.deploy.outputs.url }}\n\n*Deployed from commit: ${context.sha.substring(0, 7)}*`
            })

  deploy-backend:
    name: 🔧 Deploy Backend Services
    runs-on: ubuntu-latest
    needs: pre-deploy
    if: needs.pre-deploy.outputs.should-deploy == 'true'
    environment:
      name: staging
    strategy:
      matrix:
        service: [auth-service, prediction-service, data-ingestion-service, ai-assistant-service]
    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🐍 Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: 📦 Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.11'

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔑 Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CONTAINER_REGISTRY }}
          username: ${{ secrets.CONTAINER_REGISTRY_USERNAME }}
          password: ${{ secrets.CONTAINER_REGISTRY_PASSWORD }}

      - name: 📦 Install dependencies
        working-directory: packages/${{ matrix.service }}
        run: pdm install --prod

      - name: 🏗️ Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: packages/${{ matrix.service }}
          file: packages/${{ matrix.service }}/Dockerfile
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/sportsclub-${{ matrix.service }}:staging
            ${{ secrets.CONTAINER_REGISTRY }}/sportsclub-${{ matrix.service }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🚀 Deploy to staging environment
        run: |
          echo "🚀 Deploying ${{ matrix.service }} to staging"
          # Add your deployment commands here (e.g., kubectl, docker-compose, etc.)
          # This will depend on your infrastructure setup

  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-backend]
    if: always() && (needs.deploy-frontend.result == 'success' || needs.deploy-backend.result == 'success')
    steps:
      - name: 🏥 Check frontend health
        run: |
          echo "🔍 Checking frontend health..."
          # Add health check for frontend
          curl -f ${{ secrets.STAGING_FRONTEND_URL }}/api/health || exit 1

      - name: 🏥 Check backend services health
        run: |
          echo "🔍 Checking backend services health..."
          # Add health checks for backend services
          for service in auth prediction data-ingestion ai-assistant; do
            echo "Checking $service service..."
            curl -f ${{ secrets.STAGING_API_URL }}/$service/health || exit 1
          done

      - name: 📊 Run smoke tests
        run: |
          echo "🧪 Running smoke tests..."
          # Add basic smoke tests here

  notify:
    name: 📢 Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-backend, health-check]
    if: always()
    steps:
      - name: 📢 Notify success
        if: needs.health-check.result == 'success'
        run: |
          echo "✅ Staging deployment successful!"
          # Add notification logic (Slack, Discord, etc.)

      - name: 📢 Notify failure
        if: needs.health-check.result == 'failure'
        run: |
          echo "❌ Staging deployment failed!"
          # Add notification logic for failures
